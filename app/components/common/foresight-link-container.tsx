'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { type ForesightRect } from 'js.foresight';
import {
  determineLinkType,
  getRelAttribute,
  LinkType,
  isExternalUrl,
} from '@/utils/link-utils';
import { getForesightManager, isForesightAvailable } from '@/utils/foresight-config';

interface ForesightLinkContainerProps {
  iconUrl: string;
  title: string;
  caption: string;
  notificationCount?: number;
  iconClass?: string;
  rootClass?: string;
  href: string;
  linkType?: LinkType;
  hitSlop?: number | ForesightRect;
  unregisterOnCallback?: boolean;
  name?: string;
  disabled?: boolean;
  fallbackPrefetch?: boolean;
  onClick?: () => void;
}

/**
 * ForesightLinkContainer Component
 * 
 * An enhanced version of LinkContainer that integrates ForesightJS for predictive prefetching.
 * This component provides a rich, interactive container with icon, title, and caption that
 * intelligently prefetches content based on user intent.
 * 
 * Features:
 * - Predictive prefetching with ForesightJS integration
 * - Smooth animations with Framer Motion
 * - SEO-friendly link attributes
 * - Touch device fallback
 * - Notification badge support
 * - Customizable styling and behavior
 * 
 * @example
 * // Basic usage
 * <ForesightLinkContainer
 *   iconUrl="/svg/shop.svg"
 *   title="Shop Now"
 *   caption="Explore our latest products"
 *   href="/products"
 * />
 * 
 * @example
 * // With custom hit slop and notification
 * <ForesightLinkContainer
 *   iconUrl="/svg/cart.svg"
 *   title="Shopping Cart"
 *   caption="View your items"
 *   href="/cart"
 *   notificationCount={3}
 *   hitSlop={30}
 *   name="cart-link"
 * />
 */
const ForesightLinkContainer = ({
  iconUrl,
  title,
  caption,
  notificationCount,
  iconClass,
  rootClass,
  href,
  linkType,
  hitSlop = 0,
  unregisterOnCallback = true,
  name,
  disabled = false,
  fallbackPrefetch = false,
  onClick,
}: ForesightLinkContainerProps) => {
  const router = useRouter();
  const containerRef = useRef<HTMLDivElement>(null);
  const [isTouchDevice, setIsTouchDevice] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);

  // Determine link type and attributes
  const type = determineLinkType(href, linkType);
  const rel = getRelAttribute(type);
  const isExternal = isExternalUrl(href);

  useEffect(() => {
    // Skip registration if disabled or no href
    if (disabled || !href) {
      return;
    }

    const currentRef = containerRef.current;
    if (!currentRef || !isForesightAvailable()) {
      // Fallback: set touch device state for standard prefetch behavior
      setIsTouchDevice(fallbackPrefetch);
      return;
    }

    const foresightManager = getForesightManager();
    if (!foresightManager) {
      setIsTouchDevice(fallbackPrefetch);
      return;
    }

    try {
      const registrationResult = foresightManager.register({
        element: currentRef,
        callback: () => {
          // Only prefetch internal links
          if (!isExternal) {
            router.prefetch(href);
          }
        },
        hitSlop,
        name: name || `foresight-container-${title.toLowerCase().replace(/\s+/g, '-')}`,
        unregisterOnCallback,
      });

      setIsTouchDevice(registrationResult.isTouchDevice);
      setIsRegistered(true);

      // Return cleanup function
      return () => {
        try {
          registrationResult.unregister();
          setIsRegistered(false);
        } catch (error) {
          console.warn('Error unregistering ForesightJS container:', error);
        }
      };
    } catch (error) {
      console.warn('Error registering ForesightJS container:', error);
      // Fallback to standard behavior
      setIsTouchDevice(fallbackPrefetch);
    }
  }, [href, hitSlop, name, title, unregisterOnCallback, router, isExternal, disabled, fallbackPrefetch]);

  const handleClick = () => {
    if (disabled) {
      return;
    }

    // Call custom onClick handler if provided
    if (onClick) {
      onClick();
    }

    if (isExternal) {
      // For external links, open in new tab with proper attributes
      const a = document.createElement('a');
      a.href = href;
      a.rel = rel || 'noopener';
      a.target = '_blank';
      a.click();
    } else {
      // For internal links, use router
      router.push(href);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (disabled) {
      return;
    }

    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleClick();
    }
  };

  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      aria-disabled={disabled}
      aria-label={`${title}: ${caption}`}
      className={clsx(
        rootClass,
        'flex lg:flex-col lg:flex-center pl-[13px] py-[15px] pr-[18px] lg:p-[10px] lg:w-[218px] lg:min-h-[154px] bg-white dark:bg-[#3E424C] rounded-[6px] relative',
        disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
      )}
      data-foresight-registered={isRegistered}
      data-href={href}
      data-link-type={type}
      data-rel={rel}
      initial={{ opacity: 0, y: 20 }}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      ref={containerRef}
      role="button"
      style={{ boxShadow: '0px 1px 5px 0px rgba(0, 0, 0, 0.07)' }}
      tabIndex={disabled ? -1 : 0}
      transition={{ duration: 0.4 }}
      whileHover={
        disabled
          ? {}
          : {
              scale: 1.03,
              boxShadow: '0px 5px 15px rgba(0, 0, 0, 0.1)',
              transition: { duration: 0.3 },
            }
      }
      whileTap={disabled ? {} : { scale: 0.98 }}
    >
      {/* Notification Badge */}
      {notificationCount && notificationCount > 0 && (
        <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold z-10">
          {notificationCount > 99 ? '99+' : notificationCount}
        </div>
      )}

      {/* Icon */}
      <div className="flex-shrink-0 lg:mb-[10px]">
        <Image
          alt={`${title} icon`}
          className={clsx(
            'object-contain',
            iconClass || 'w-[18px] h-[18px] lg:w-[23px] lg:h-[23px]'
          )}
          height={24}
          src={iconUrl}
          width={24}
        />
      </div>

      {/* Content */}
      <div className="flex-1 ml-[13px] lg:ml-0 lg:text-center">
        <h3 className="text-[12px] lg:text-[14px] font-semibold text-blackWhite dark:text-white mb-[2px] lg:mb-[5px]">
          {title}
        </h3>
        <p className="text-[10px] lg:text-[11px] text-gray-600 dark:text-gray-300 leading-tight">
          {caption}
        </p>
      </div>

      {/* Debug indicator (development only) */}
      {process.env.NODE_ENV === 'development' && isRegistered && (
        <div className="absolute top-1 left-1 w-2 h-2 bg-green-400 rounded-full opacity-50" 
             title="ForesightJS registered" />
      )}
    </motion.div>
  );
};

export default ForesightLinkContainer;
