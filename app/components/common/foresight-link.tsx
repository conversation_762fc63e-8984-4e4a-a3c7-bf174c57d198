'use client';

import type { LinkProps } from 'next/link';
import Link from 'next/link';
import { useEffect, useRef, useState, forwardRef } from 'react';
import { type ForesightRect } from 'js.foresight';
import { useRouter } from 'next/navigation';
import { getForesightManager, isForesightAvailable } from '@/utils/foresight-config';
import { determineLinkType, getRelAttribute, LinkType, isExternalUrl } from '@/utils/link-utils';

/**
 * Props for the ForesightLink component
 */
interface ForesightLinkProps extends Omit<LinkProps, 'prefetch'> {
  children: React.ReactNode;
  className?: string;
  hitSlop?: number | ForesightRect;
  unregisterOnCallback?: boolean;
  name?: string;
  linkType?: LinkType;
  target?: string;
  onClick?: (event: React.MouseEvent<HTMLAnchorElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLAnchorElement>) => void;
  ariaLabel?: string;
  title?: string;
  fallbackPrefetch?: boolean;
  disabled?: boolean;
}

/**
 * ForesightLink Component
 * 
 * A Next.js Link wrapper that integrates ForesightJS for predictive prefetching.
 * Automatically detects user intent through mouse trajectory analysis and triggers
 * prefetching only when necessary, reducing unnecessary network requests.
 * 
 * Features:
 * - Predictive prefetching based on mouse trajectory
 * - Graceful fallback for touch devices
 * - SEO-friendly rel attributes for external links
 * - TypeScript support with comprehensive props
 * - Error handling and SSR safety
 * 
 * @example
 * // Basic usage
 * <ForesightLink href="/products">
 *   View Products
 * </ForesightLink>
 * 
 * @example
 * // With custom hit slop and name
 * <ForesightLink 
 *   href="/checkout" 
 *   hitSlop={30}
 *   name="checkout-button"
 * >
 *   Proceed to Checkout
 * </ForesightLink>
 * 
 * @example
 * // External link with sponsored rel attribute
 * <ForesightLink 
 *   href="https://partner.com"
 *   linkType={LinkType.EXTERNAL_SPONSORED}
 * >
 *   Visit Partner
 * </ForesightLink>
 */
export const ForesightLink = forwardRef<HTMLAnchorElement, ForesightLinkProps>(
  ({
    children,
    className,
    hitSlop = 0,
    unregisterOnCallback = true,
    name = '',
    linkType,
    target,
    onClick,
    onKeyDown,
    ariaLabel,
    title,
    fallbackPrefetch = false,
    disabled = false,
    ...props
  }, ref) => {
    const linkRef = useRef<HTMLAnchorElement>(null);
    const [isTouchDevice, setIsTouchDevice] = useState(false);
    const [isRegistered, setIsRegistered] = useState(false);
    const router = useRouter();

    // Determine link type and rel attributes
    const href = props.href.toString();
    const type = determineLinkType(href, linkType);
    const rel = getRelAttribute(type);
    const isExternal = isExternalUrl(href);

    useEffect(() => {
      // Skip registration if disabled or no href
      if (disabled || !href) {
        return;
      }

      const currentRef = linkRef.current;
      if (!currentRef || !isForesightAvailable()) {
        // Fallback: use standard prefetch if ForesightJS is not available
        setIsTouchDevice(fallbackPrefetch);
        return;
      }

      const foresightManager = getForesightManager();
      if (!foresightManager) {
        setIsTouchDevice(fallbackPrefetch);
        return;
      }

      try {
        const registrationResult = foresightManager.register({
          element: currentRef,
          callback: () => {
            // Only prefetch internal links
            if (!isExternal) {
              router.prefetch(href);
            }
          },
          hitSlop,
          name: name || `foresight-link-${href}`,
          unregisterOnCallback,
        });

        setIsTouchDevice(registrationResult.isTouchDevice);
        setIsRegistered(true);

        // Return cleanup function
        return () => {
          try {
            registrationResult.unregister();
            setIsRegistered(false);
          } catch (error) {
            console.warn('Error unregistering ForesightJS element:', error);
          }
        };
      } catch (error) {
        console.warn('Error registering ForesightJS element:', error);
        // Fallback to standard behavior
        setIsTouchDevice(fallbackPrefetch);
      }
    }, [href, hitSlop, name, unregisterOnCallback, router, isExternal, disabled, fallbackPrefetch]);

    // Handle click events
    const handleClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
      if (disabled) {
        event.preventDefault();
        return;
      }

      // Call custom onClick handler if provided
      if (onClick) {
        onClick(event);
      }

      // For external links, handle opening in new tab if no target specified
      if (isExternal && !target) {
        event.preventDefault();
        window.open(href, '_blank', 'noopener');
      }
    };

    // Handle keyboard events
    const handleKeyDown = (event: React.KeyboardEvent<HTMLAnchorElement>) => {
      if (disabled && (event.key === 'Enter' || event.key === ' ')) {
        event.preventDefault();
        return;
      }

      if (onKeyDown) {
        onKeyDown(event);
      }
    };

    // For external links, use anchor tag
    if (isExternal) {
      return (
        <a
          aria-disabled={disabled}
          aria-label={ariaLabel}
          className={className}
          href={disabled ? undefined : href}
          onClick={handleClick}
          onKeyDown={handleKeyDown}
          ref={ref || linkRef}
          rel={rel}
          tabIndex={disabled ? -1 : undefined}
          target={target || '_blank'}
          title={title}
        >
          {children}
        </a>
      );
    }

    // For internal links, use Next.js Link
    return (
      <Link
        {...props}
        aria-disabled={disabled}
        aria-label={ariaLabel}
        className={className}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        prefetch={isTouchDevice || fallbackPrefetch}
        ref={ref || linkRef}
        tabIndex={disabled ? -1 : undefined}
        target={target}
        title={title}
      >
        {children}
      </Link>
    );
  }
);

ForesightLink.displayName = 'ForesightLink';

export default ForesightLink;
