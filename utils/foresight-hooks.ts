'use client';

import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useRef } from 'react';
import { type ForesightRect } from 'js.foresight';
import { determineLinkType, isExternalUrl, LinkType } from './link-utils';
import { getForesightManager, isForesightAvailable } from './foresight-config';

/**
 * Options for ForesightJS navigation registration
 */
export interface ForesightNavigationOptions {
  hitSlop?: number | ForesightRect;
  unregisterOnCallback?: boolean;
  name?: string;
  linkType?: LinkType;
  fallbackPrefetch?: boolean;
}

/**
 * Result from ForesightJS navigation registration
 */
export interface ForesightNavigationResult {
  isTouchDevice: boolean;
  isRegistered: boolean;
  unregister: () => void;
}

/**
 * Hook for ForesightJS-enhanced navigation
 * 
 * This hook provides utilities for integrating ForesightJS with programmatic navigation,
 * allowing components to register elements for predictive prefetching and handle
 * navigation with proper SEO attributes.
 * 
 * @example
 * const { navigateTo, registerElement } = useForesightNavigation();
 * 
 * // Programmatic navigation
 * const handleClick = () => {
 *   navigateTo('/products');
 * };
 * 
 * // Register an element for predictive prefetching
 * const buttonRef = useRef<HTMLButtonElement>(null);
 * useEffect(() => {
 *   if (buttonRef.current) {
 *     const { unregister } = registerElement(buttonRef.current, '/products');
 *     return unregister;
 *   }
 * }, []);
 */
export const useForesightNavigation = () => {
  const router = useRouter();

  /**
   * Navigate to a URL with proper handling of external links and SEO attributes
   */
  const navigateTo = useCallback((
    url: string,
    linkType?: LinkType,
    target?: string
  ) => {
    const type = determineLinkType(url, linkType);
    const isExternal = isExternalUrl(url);

    if (isExternal) {
      // For external links, open in new tab with proper rel attributes
      const relValue = type === LinkType.EXTERNAL ? 'noopener' :
                      type === LinkType.EXTERNAL_NOFOLLOW ? 'nofollow noopener' :
                      type === LinkType.EXTERNAL_SPONSORED ? 'sponsored noopener' :
                      'nofollow sponsored noopener';

      // Create a temporary anchor element to set the rel attribute
      const a = document.createElement('a');
      a.href = url;
      a.rel = relValue;
      a.target = target || '_blank';
      a.click();
    } else {
      // For internal links, use the Next.js router
      router.push(url);
    }
  }, [router]);

  /**
   * Register an element with ForesightJS for predictive prefetching
   */
  const registerElement = useCallback((
    element: HTMLElement,
    url: string,
    options: ForesightNavigationOptions = {}
  ): ForesightNavigationResult => {
    const {
      hitSlop = 0,
      unregisterOnCallback = true,
      name,
      linkType,
      fallbackPrefetch = false,
    } = options;

    // Default result for fallback scenarios
    const fallbackResult: ForesightNavigationResult = {
      isTouchDevice: fallbackPrefetch,
      isRegistered: false,
      unregister: () => {},
    };

    // Check if ForesightJS is available
    if (!isForesightAvailable()) {
      console.warn('ForesightJS not available, falling back to standard behavior');
      return fallbackResult;
    }

    const foresightManager = getForesightManager();
    if (!foresightManager) {
      return fallbackResult;
    }

    // Don't register external links for prefetching
    const isExternal = isExternalUrl(url);
    if (isExternal) {
      return {
        isTouchDevice: false,
        isRegistered: false,
        unregister: () => {},
      };
    }

    try {
      const registrationResult = foresightManager.register({
        element,
        callback: () => {
          // Prefetch the URL when intent is detected
          router.prefetch(url);
        },
        hitSlop,
        name: name || `foresight-element-${url}`,
        unregisterOnCallback,
      });

      return {
        isTouchDevice: registrationResult.isTouchDevice,
        isRegistered: true,
        unregister: registrationResult.unregister,
      };
    } catch (error) {
      console.warn('Error registering element with ForesightJS:', error);
      return fallbackResult;
    }
  }, [router]);

  /**
   * Prefetch a URL manually (useful for programmatic prefetching)
   */
  const prefetchUrl = useCallback((url: string) => {
    if (!isExternalUrl(url)) {
      router.prefetch(url);
    }
  }, [router]);

  return {
    navigateTo,
    registerElement,
    prefetchUrl,
    isForesightAvailable: isForesightAvailable(),
  };
};

/**
 * Hook for registering a single element with ForesightJS
 * 
 * This hook simplifies the process of registering a single element
 * with ForesightJS and handles cleanup automatically.
 * 
 * @example
 * const buttonRef = useRef<HTMLButtonElement>(null);
 * const { isTouchDevice, isRegistered } = useForesightElement(
 *   buttonRef,
 *   '/products',
 *   { hitSlop: 20, name: 'products-button' }
 * );
 */
export const useForesightElement = (
  elementRef: React.RefObject<HTMLElement>,
  url: string,
  options: ForesightNavigationOptions = {}
) => {
  const { registerElement } = useForesightNavigation();
  const unregisterRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element || !url) {
      return;
    }

    const result = registerElement(element, url, options);
    unregisterRef.current = result.unregister;

    // Return cleanup function
    return () => {
      if (unregisterRef.current) {
        unregisterRef.current();
        unregisterRef.current = null;
      }
    };
  }, [elementRef, url, options, registerElement]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (unregisterRef.current) {
        unregisterRef.current();
      }
    };
  }, []);

  return {
    isForesightAvailable: isForesightAvailable(),
  };
};

/**
 * Hook for batch registering multiple elements
 * 
 * Useful for components that need to register multiple navigation elements
 * with different URLs and configurations.
 * 
 * @example
 * const { registerElements, unregisterAll } = useForesightBatch();
 * 
 * useEffect(() => {
 *   const elements = [
 *     { element: ref1.current, url: '/page1', options: { hitSlop: 20 } },
 *     { element: ref2.current, url: '/page2', options: { hitSlop: 30 } },
 *   ];
 *   
 *   registerElements(elements);
 *   return unregisterAll;
 * }, []);
 */
export const useForesightBatch = () => {
  const { registerElement } = useForesightNavigation();
  const unregisterFunctions = useRef<(() => void)[]>([]);

  const registerElements = useCallback((
    elements: Array<{
      element: HTMLElement | null;
      url: string;
      options?: ForesightNavigationOptions;
    }>
  ) => {
    // Clear previous registrations
    unregisterFunctions.current.forEach(fn => fn());
    unregisterFunctions.current = [];

    // Register new elements
    elements.forEach(({ element, url, options }) => {
      if (element) {
        const result = registerElement(element, url, options);
        unregisterFunctions.current.push(result.unregister);
      }
    });
  }, [registerElement]);

  const unregisterAll = useCallback(() => {
    unregisterFunctions.current.forEach(fn => fn());
    unregisterFunctions.current = [];
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return unregisterAll;
  }, [unregisterAll]);

  return {
    registerElements,
    unregisterAll,
  };
};
